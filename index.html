<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interactive Web Design Styles Showcase</title>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-YR81E31MX4"></script>
    <script>
      window.dataLayer = window.dataLayer || []
      
      function gtag() {
        dataLayer.push(arguments)
      }
      gtag('js', new Date())
      gtag('config', 'G-YR81E31MX4')
    </script>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Covered+By+Your+Grace&family=Permanent+Marker&display=swap');
      :root {
        /* Customize these to change stripe look */
        --stripe-color: rgba(252, 255, 75, 0.85);
        /* color of the stripe */
        --dark-stripe-color: rgba(64, 60, 0, 0.95);
        /* color of the stripe */
        --stripe-width: 25px;
        /* thickness of each stripe */
        --stripe-spacing: 50px;
        /* distance from start of one stripe to the next */
        --stripe-angle: 135deg;
        /* direction of the stripes */
        --opacity: 0.8;
        /* opacity of the overlay */
        --bg-color: rgba(255, 255, 255, 0.9);
        /* background color of the overlay */
        --inverse-bg-color: rgb(0, 0, 0);
        /* background color of the inverse overlay */
        --padding: 1rem;
        /* padding of the overlay */
        --blur: 10px;
        /* blur effect */
        --rounded: 10px;
        /* border radius of the overlay */
        --accent-color: rgba(0, 122, 204, 0.9);
        /* color of the accent */
        --inverse-accent-color: rgba(164, 163, 228, 0.9);
        /* color of the inverse accent */
        --text-color: rgba(51, 51, 51, 0.9);
        /* color of the text */
        --inverse-text-color: rgba(255, 255, 255, 0.9);
        /* color of the inverse text */
      }
      
      /* Base styles */
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
        line-height: 1.6;
      }
      
      /* Ensure site header stays at the very top */
      #site-header {
        position: sticky;
        z-index: 100;
        /* Ensure header stays above hero section */
      }
      
      .intro {
        display: flex;
        flex-direction: column;
        padding: 2rem;
        background-color: var(--bg-color);
        backdrop-filter: var(--blur);
        color: var(--text-color);
        text-align: center;
        margin: 0 auto;
        max-width: 80%;
        border-radius: var(--rounded);
        box-shadow: 0 4px 8px var(--shadow-dark), 0 2px 4px var(--shadow-light);
        margin-bottom: 2rem;
      }
      
      .into h3 {
        align-items: center;
        justify-content: center;
      }
      
      .intro p {
        align-items: left;
        justify-content: center;
      }
      
      #outro {
        padding-top: 5rem;
      }
      
      .flex-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
      }
      
      .grid-item {
        flex: 1 1 300px;
        border: 1px solid var(--accent-color);
        padding: 1rem;
        margin: 0.5rem 0;
        box-sizing: border-box;
        background-color: var(--bg-color);
        color: var(--text-color);
        radius: var(--rounded);
      }
      
      .grid-item p {
        margin: 0.5rem;
      }
      
      /* Hero Section and Video Background */
      .hero-section {
        position: fixed;
        /* Change from relative to fixed */
        top: 0;
        /* Add top positioning */
        left: 0;
        /* Add left positioning */
        width: 100%;
        /* Simplify width */
        height: 100vh;
        /* Keep full viewport height */
        z-index: -1;
        /* Ensure it stays behind content */
      }
      
      .video-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
      }
      
      /* Video Container Styling */
      #traffic_videos {
        position: relative;
        width: 100%;
        height: 100%;
        background-color: black;
        /* Fallback color */
      }
      
      /* Individual Video Elements */
      [id$='_videos_1'],
      [id$='_videos_2'] {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        /* Maintain aspect ratio while covering */
        transition: opacity 1s ease-in-out;
        opacity: 0;
        display: none;
      }
      
      /* Video Overlay */
      .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        z-index: 1;
      }
      
      /* Content Wrapper Adjustments */
      .content-wrapper {
        position: relative;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--padding);
        z-index: 2;
        margin: 0;
        /* Remove any margins */
      }
      
      .content-wrapper h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        align-items: center;
        justify-content: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      }
      
      .content-wrapper p {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        align-items: left;
        justify-content: left;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }
      
      /* Header Content */
      .header-content {
        flex: 1 1 33%;
        padding: 2rem;
        border-radius: var(--rounded);
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        backdrop-filter: var(--blur);
        opacity: var(--opacity);
        max-height: 35%;
        max-width: 35%;
      }
      
      .header-content h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
      }
      
      .header-content p {
        font-size: 1.2rem;
        line-height: 1.6;
      }
      
      .era-timeline {
        top: 0;
        left: 0;
        width: 80%;
        height: 80vh;
        margin: auto;
        padding: 2rem;
        overflow: hidden;
        /* display: none; /* Hidden by default, shown in cinematic mode */
      }
      
      article.style-card {
        padding: 1rem;
        margin-bottom: 1rem;
      }
      
      @media (max-width: 992px) {
        article.style-card {
          max-width: calc(50% - 20px);
        }
      }
      
      @media (max-width: 576px) {
        article.style-card {
          max-width: 100%;
        }
      }
      
      article.style-card:hover {
        transform: scale(1.02);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
      
      .style-title {
        font-size: 1.25rem;
        cursor: pointer;
        color: rgba(0, 122, 204, 0.9);
        display: inline-block;
        margin-bottom: 0.25rem;
      }
      
      .style-era {
        font-style: italic;
        margin-bottom: 0.5rem;
      }
      
      .style-description {
        margin-bottom: 0.5rem;
      }
      
      .style-links a {
        margin-right: 0.5rem;
        color: #007acc;
      }
      
      /* Hide legacy extras */
      .extra-under,
      .extra-counter,
      .extra-flash-intro,
      #main-table,
      .frame-nav,
      .frame-content,
      .beta-badge,
      .card-texture,
      .grid-overlay {
        display: none !important;
      }
      
      /* Gantt chart interactive styles */
      .mermaid .task,
      .mermaid text.taskText,
      .mermaid text.sectionTitle {
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .mermaid .task:hover {
        opacity: 0.8;
        transform: translateY(-2px);
      }
      
      .mermaid {
        background-color: var(--bg-color);
        backdrop-filter: var(--blur);
      }
      
      .mermaid text.taskText:hover {
        font-weight: bold;
      }
      
      .mermaid text.sectionTitle:hover {
        font-weight: bold;
        text-decoration: underline;
      }
      
      /* Add tooltips to Gantt elements */
      .mermaid .task,
      .mermaid text.taskText {
        position: relative;
      }
      
      /* Container for all ads */
      .ad-group {
        width: 80%;
        margin: 2rem auto;
        padding: 1rem;
        border-radius: var(--rounded);
        background-color: var(--content-bg);
        box-shadow: 0 4px 8px var(--shadow-dark), 0 2px 4px var(--shadow-light);
      }
      
      /* Each ad line */
      .ad-group .ad-line {
        margin: 0.75rem 0;
        padding: 0.75rem 1rem;
        background-color: var(--bg-color);
      
        transition: transform 0.3s ease;
        border: 1px solid var(--accent-color);
      }
      
      /* Hover effect for ad lines */
      .ad-group .ad-line:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px var(--shadow-dark);
      }
      
      /* Link styling */
      .ad-group .ad-line a {
        display: block;
        color: var(--text-color);
        font-weight: 600;
        text-decoration: none;
        transition: color 0.3s ease;
      }
      
      /* Link hover state */
      .ad-group .ad-line a:hover {
        color: var(--text-color);
        text-decoration: underline;
      }
      
      /* ===== Main Container & Layout ===== */
      #main-core {
        max-width: 1300px;
        margin: 0 auto;
      }

      
      
    </style>
    <style>
      /* Dark mode toggle styles */
      .dark-mode-toggle {
        position: fixed;
        top: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 8px 12px;
        border-radius: 20px;
        z-index: 1000;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }
      
      .current-style-display {
        position: fixed;
        top: 70px;
        right: 20px;
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 8px 12px;
        border-radius: 20px;
        z-index: 1000;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        color: white;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }
      
      #current-theme {
        margin-left: 5px;
        color: #2196f3;
      }
      
      .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
        margin-right: 10px;
      }
      
      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }
      
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
      }
      
      .slider:before {
        position: absolute;
        content: '';
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.4s;
      }
      
      input:checked + .slider {
        background-color: #2196f3;
      }
      
      input:checked + .slider:before {
        transform: translateX(26px);
      }
      
      .slider.round {
        border-radius: 34px;
      }
      
      .slider.round:before {
        border-radius: 50%;
      }
      
      .dark-mode-toggle span {
        color: white;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }
    </style>

    <!-- Dynamic theme CSS will be injected here -->
    <style id="theme-style"></style>

    <!-- Add Mermaid JS library -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
  </head>

  <body>
    <div class="hero-section">
      <div class="video-background">
        <div id="color_videos">
          <video id="color_videos_1" muted playsinline webkit-playsinline></video>
          <video id="color_videos_2" muted playsinline webkit-playsinline></video>
        </div>
      </div>
    </div>
    <div class="counter">
      <div>Website visit count:</div>
      <div id="vistor-counter"></div>
    </div>
    <div id="theme-style"></div>
    <div class="dark-mode-toggle">
      <label class="switch">
        <input type="checkbox" id="dark-mode-toggle" />
        <span class="slider round"></span>
      </label>
      <span>Dark Mode</span>
    </div>
    <div class="current-style-display">
      <span>Style:</span>
      <span id="current-theme">Default</span>
    </div>
    <div id="under-construction">
      <img src="https://gifgifs.com/animations/webdesign-elements/under-construction/webpage_construction.gif" alt="Under Construction" />
    </div>
    <div class="content-wrapper">
      <div class="header-content">
        <h1>Interactive Web Design Styles Showcase</h1>
        <p>From then to now.</p>
        <p>
          <small>Click a title or press <strong>Ctrl + Shift + Space</strong> to cycle styles</small>
        </p>
      </div>
    </div>

    <section class="intro">
      <h3><strong>Overview</strong></h3>
      <p>
        The Interactive Web Design Styles Showcase is designed to guide you through the evolution of web design—from the playful, “Under Construction” days of GeoCities to the sleek, glass-blur panels of Glassmorphism. At its core, the page presents a chronological Gantt-style timeline of major design movements and pairs each era with a corresponding “style card” that highlights its defining characteristics, key dates, and reference links. You can explore each style by clicking its title or by pressing <strong>Ctrl + Shift + Space</strong>, which cycles through the various themes in sequence. Below the timeline, a live CSS editor displays the exact rules being applied, so you can see how each design aesthetic is constructed in real time.
      </p>
    </section>

    <nav aria-label="Jump to era" class="era-timeline">
      <h3>Web Design Evolution Timeline</h3>
      <div class="mermaid-wrapper">
        <div class="mermaid" id="era-timeline">
gantt
  title Web Design History
  dateFormat YYYY
  axisFormat %Y
  todayMarker off

  section 1990s
    Vernacular / Geocities    :vernacular, 1991, 1995
    Table-Based Design        :table-frames, 1993, 1998
    Framesets                 :framesets, 1993, 1998
    Flash-Based Design        :flash, 1996, 2008

  section 2000s
    Web 2.0 Glossy            :web2, 2004, 2013
    Skeuomorphic              :skeuomorphic, 2004, 2013
    Swiss Style               :swiss, 2000, 2025
    Minimalist                :minimalist, 2000, 2025

  section 2010s
    Responsive Design         :responsive, 2010, 2025
    Flat Design               :flat-design, 2010, 2025
    Parallax                  :parallax, 2012, 2016
    Single-Page App           :spa, 2012, 2016
    Progressive Web App       :pwa, 2015, 2025
    Cinematic                 :cinematic, 2010, 2019
    Brutalist                 :brutalist, 2015, 2025
    Neo-Brutalism             :neo-brutalism, 2015, 2025
    Corporate Memphis         :corporate-memphis, 2018, 2025
    WebGL & 3D                :webgl-3d, 2015, 2025

  section 2020s
    Dark Mode                 :dark-mode, 2020, 2025
    Neumorphism               :neumorphism, 2020, 2021
    Glassmorphism             :glassmorphism, 2020, 2021
    Retro Web                 :retro-web, 2020, 2025
    Y2K Web                   :y2k-web, 2020, 2025
    Inclusive Design          :inclusive-design, 2010, 2025
        </div>
      </div>
    </nav>

    <section class="css-editor">
      <h2>Current CSS</h2>
      <label for="css-editor">Edit CSS:</label>
      <textarea id="css-editor" editable placeholder="Enter your CSS here"></textarea>
    </section>

    <main>
      <!-- All 26 style cards -->
      <article class="style-card" data-index="1" id="vernacular">
        <h3 class="style-title">Vernacular / Geocities</h3>
        <p class="style-era">1991–1995</p>
        <p class="style-description">Amateur pages built with tiled backgrounds, animated GIFs, “Under Construction” signs, and visitor counters.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/GeoCities" target="_blank">GeoCities</a>
          <a href="https://en.wikipedia.org/wiki/Under_Construction" target="_blank">Under Construction</a>
        </p>
      </article>

      <article class="style-card" data-index="2" id="table-frames">
        <h3 class="style-title">Table-Based Design</h3>
        <p class="style-era">1993–1998</p>
        <p class="style-description">Using HTML tables to approximate multi-column layouts before CSS adoption.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/HTML_table" target="_blank">HTML Tables</a>
          <a href="https://en.wikipedia.org/wiki/CSS" target="_blank">CSS</a>
        </p>
      </article>

      <article class="style-card" data-index="3" id="framesets">
        <h3 class="style-title">Framesets</h3>
        <p class="style-era">1993–1998</p>
        <p class="style-description">Splitting the browser window into independent panes—navigation, content, etc.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/HTML_frame" target="_blank">HTML Frames</a>
          <a href="https://developer.mozilla.org/docs/Web/HTML/Element/frame" target="_blank">MDN Frames</a>
        </p>
      </article>

      <article class="style-card" data-index="4" id="flash">
        <h3 class="style-title">Flash-Based Design</h3>
        <p class="style-era">1996–2008</p>
        <p class="style-description">Rich animations, interactive menus, and full-screen intros created with Macromedia Flash.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Adobe_Flash" target="_blank">Adobe Flash</a>
          <a href="https://en.wikipedia.org/wiki/Adobe_Animate" target="_blank">Adobe Animate</a>
        </p>
      </article>

      <article class="style-card" data-index="5" id="web2">
        <h3 class="style-title">Web 2.0 Glossy</h3>
        <p class="style-era">2004–2013</p>
        <p class="style-description">Glossy buttons, drop shadows, reflections, stock-photo headers, and “beta” tags.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Web_2.0" target="_blank">Web 2.0</a>
          <a href="https://www.wired.com/" target="_blank">WIRED</a>
        </p>
      </article>

      <article class="style-card" data-index="6" id="skeuomorphic">
        <h3 class="style-title">Skeuomorphic</h3>
        <p class="style-era">2004–2013</p>
        <p class="style-description">Interfaces mimicking real-world textures (leather, metal, PostIt Notes).</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Skeuomorph" target="_blank">Skeuomorph</a>
          <a href="https://en.wikipedia.org/wiki/Apple_Human_Interface_Guidelines#Skeuomorphism" target="_blank">iOS Skeuomorphism</a>
        </p>
      </article>

      <article class="style-card" data-index="7" id="swiss">
        <h3 class="style-title">Swiss Style</h3>
        <p class="style-era">2000s–present</p>
        <p class="style-description">Grid-based layouts, strong typography, and generous whitespace.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Swiss_Style" target="_blank">International Typographic Style</a>
          <a href="https://en.wikipedia.org/wiki/Helvetica" target="_blank">Helvetica</a>
        </p>
      </article>

      <article class="style-card" data-index="8" id="minimalist">
        <h3 class="style-title">Minimalist</h3>
        <p class="style-era">2000s–present</p>
        <p class="style-description">Stripping interfaces to essentials—white space, high-contrast typography, and minimal UI elements.</p>
        <p class="style-links">
          <a href="https://www.nngroup.com/articles/minimalist-web-design/" target="_blank">NNG Guide</a>
          <a href="https://en.wikipedia.org/wiki/Minimalism" target="_blank">Minimalism</a>
        </p>
      </article>

      <article class="style-card" data-index="9" id="responsive">
        <h3 class="style-title">Responsive</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Fluid grids, flexible images, and CSS media queries ensure layouts adapt.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Responsive_web_design" target="_blank">Responsive</a>
          <a href="https://developer.mozilla.org/docs/Web/CSS/Media_Queries" target="_blank">MDN Media Queries</a>
        </p>
      </article>

      <article class="style-card" data-index="10" id="mobile-first">
        <h3 class="style-title">Mobile-First</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Designing for small screens first, then enhancing for larger viewports.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Mobile-first_design" target="_blank">Mobile-First</a>
          <a href="https://www.smashingmagazine.com/2011/01/guidelines-for-responsive-web-design/" target="_blank">Smashing Mag.</a>
        </p>
      </article>

      <article class="style-card" data-index="11" id="flat-design">
        <h3 class="style-title">Flat Design</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Minimalist UI with simple elements, flat colors, no textures.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Flat_design" target="_blank">Flat Design</a>
          <a href="https://en.wikipedia.org/wiki/Metro_(design_language)" target="_blank">Metro UI</a>
        </p>
      </article>

      <article class="style-card" data-index="12" id="semi-flat">
        <h3 class="style-title">Semi-Flat</h3>
        <p class="style-era">2010–present</p>
        <p class="style-description">Flat foundations augmented with subtle shadows or minimal gradients.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Flat_design#Flat_2.0" target="_blank">Flat 2.0</a>
          <a href="https://uxplanet.org/semi-flat-design-principles-and-examples-6d1d90d82469" target="_blank">UX Planet</a>
        </p>
      </article>

      <article class="style-card" data-index="13" id="parallax">
        <h3 class="style-title">Parallax</h3>
        <p class="style-era">2012–2016</p>
        <p class="style-description">Layered backgrounds move at different speeds for depth.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Parallax_scrolling" target="_blank">Parallax</a>
          <a href="https://developer.mozilla.org/docs/Web/CSS/transform-function/translate3d" target="_blank">MDN</a>
        </p>
      </article>

      <article class="style-card" data-index="14" id="spa">
        <h3 class="style-title">Single-Page App</h3>
        <p class="style-era">2012–2016</p>
        <p class="style-description">JavaScript-driven sites loading content dynamically on one page.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Single-page_application" target="_blank">SPA</a>
          <a href="https://angular.io/guide/architecture" target="_blank">Angular</a>
        </p>
      </article>

      <article class="style-card" data-index="15" id="pwa">
        <h3 class="style-title">Progressive Web App</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">App-like experiences with service workers, offline support, push notifications.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Progressive_web_app" target="_blank">PWA</a>
          <a href="https://web.dev/what-are-pwas/" target="_blank">web.dev</a>
        </p>
      </article>

      <article class="style-card" data-index="16" id="cinematic">
        <h3 class="style-title">Cinematic</h3>
        <p class="style-era">2010s</p>
        <p class="style-description">Full-screen video headers, ambient soundscapes, story layouts.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Video_background" target="_blank">Video Background</a>
          <a href="https://webflow.com/blog/video-backgrounds" target="_blank">Webflow</a>
        </p>
      </article>

      <article class="style-card" data-index="17" id="brutalist">
        <h3 class="style-title">Brutalist</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">Stark pages emphasizing raw HTML, plain fonts, bare-bones layout.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Brutalist_Web_Design" target="_blank">Brutalist Web</a>
          <a href="https://verpex.com/blog/brutalist-web-design/" target="_blank">Verpex</a>
        </p>
      </article>

      <article class="style-card" data-index="18" id="neo-brutalism">
        <h3 class="style-title">Neo-Brutalism</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">Brutalist structure combined with neon or saturated color accents.</p>
        <p class="style-links">
          <a href="https://web.dev/neo-brutalism/" target="_blank">web.dev</a>
          <a href="https://fireart.studio/blog/neo-brutalism/" target="_blank">Fireart</a>
        </p>
      </article>

      <article class="style-card" data-index="19" id="dark-mode">
        <h3 class="style-title">Dark Mode</h3>
        <p class="style-era">2020–present</p>
        <p class="style-description">Light-on-dark color schemes reduce eye strain and feel modern.</p>
        <p class="style-links">
          <a href="https://bluecompass.com/articles/dark-mode-web-design/" target="_blank">Bluecompass</a>
          <a href="https://css-tricks.com/dark-modes-with-css/" target="_blank">CSS-Tricks</a>
        </p>
      </article>

      <article class="style-card" data-index="20" id="neumorphism">
        <h3 class="style-title">Neumorphism</h3>
        <p class="style-era">2020</p>
        <p class="style-description">Soft inner/outer shadows create embossed UI elements.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Neumorphism" target="_blank">Wikipedia</a>
          <a href="https://uxdesign.cc/neumorphism-in-user-interfaces-b47cef3bf3a6" target="_blank">UX Design</a>
        </p>
      </article>

      <article class="style-card" data-index="21" id="glassmorphism">
        <h3 class="style-title">Glassmorphism</h3>
        <p class="style-era">2020</p>
        <p class="style-description">Translucent panels with backdrop blur convey depth.</p>
        <p class="style-links">
          <a href="https://www.nngroup.com/articles/glassmorphism/" target="_blank">NNG</a>
          <a href="https://css-tricks.com/glassmorphism-using-backdrop-filter/" target="_blank">CSS-Tricks</a>
        </p>
      </article>

      <article class="style-card" data-index="22" id="corporate-memphis">
        <h3 class="style-title">Corporate Memphis</h3>
        <p class="style-era">2018–present</p>
        <p class="style-description">Flat geometric illustrations with bendy characters and bright color.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Corporate_Memphis" target="_blank">Wikipedia</a>
          <a href="https://medium.com/corporate-memphis" target="_blank">Medium</a>
        </p>
      </article>
      <article class="style-card" data-index="23" id="retro-web">
        <h3 class="style-title">Retro Web</h3>
        <p class="style-era">2020s</p>
        <p class="style-description">Nostalgic callbacks to ’90s/00s—pixel art, marquee text, neon palettes.</p>
        <p class="style-links">
          <a href="https://fireart.studio/blog/retro-web-design/" target="_blank">Fireart</a>
          <a href="https://99designs.com/blog/web-design/trends/nostalgic-web-design-trends/" target="_blank">99designs</a>
        </p>
      </article>

      <article class="style-card" data-index="24" id="y2k-web">
        <h3 class="style-title">Y2K Web</h3>
        <p class="style-era">2020s</p>
        <p class="style-description">Chrome buttons, metallic gradients, playful typography.</p>
        <p class="style-links">
          <a href="https://fireart.studio/blog/y2k-web-design/" target="_blank">Fireart</a>
          <a href="https://medium.com/y2k-web" target="_blank">Medium</a>
        </p>
      </article>

      <article class="style-card" data-index="25" id="webgl-3d">
        <h3 class="style-title">WebGL & 3D</h3>
        <p class="style-era">2015–present</p>
        <p class="style-description">Interactive 3D graphics, model viewers, VR/AR in browser.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/WebGL" target="_blank">WebGL</a>
          <a href="https://threejs.org/" target="_blank">three.js</a>
        </p>
      </article>

      <article class="style-card" data-index="26" id="inclusive-design">
        <h3 class="style-title">Inclusive Design</h3>
        <p class="style-era">Ongoing</p>
        <p class="style-description">ARIA roles, semantic HTML, user-centric accessibility.</p>
        <p class="style-links">
          <a href="https://en.wikipedia.org/wiki/Accessible_design" target="_blank">Accessible Design</a>
          <a href="https://www.w3.org/WAI/fundamentals/accessibility-intro/" target="_blank">W3C</a>
        </p>
      </article>
    </main>
    <section id="outro" class="intro outro">
      <h3><strong>Under The Hood</strong></h3>
      <p>Under the hood, the page relies on a combination of HTML, CSS, and JavaScript (with jQuery) to switch between design themes dynamically. A &lt;style id="theme-style"&gt; element is injected into the document head to override default styles with the current theme’s custom CSS. The page initializes a themes array in the video.js script, where each object includes a theme name and its CSS rules; a separate dark-mode variant is generated for each theme. When you trigger a theme change—either by clicking on a card, Gantt Timeline element, or using the keyboard shortcut—the script updates the injected CSS, reformats it for readability in the editor, and logs events (including Google Analytics tracking and console messages) for debugging. Special handling is included for cinematic themes, which preload and alternate background videos to recreate the immersive, full-screen intro experiences of earlier Flash-driven sites</p>
    </section>

    <section class="html-editor">
      <h2>Current HTML</h2>
      <textarea id="html-editor" editable></textarea>
    </section>

    <!-- jQuery & Migrate -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-migrate-3.4.1.min.js"></script>
    <script src="https://unpkg.com/prettier@2.8.8/standalone.js"></script>
    <script src="https://unpkg.com/prettier@2.8.8/parser-postcss.js"></script>
    <script>
      // Visitor counter code
      var counterContainer = document.querySelector('#vistor-counter')
      var resetButton = document.querySelector('#reset')
      var visitCount = localStorage.getItem('page_view')
      
      // Check if page_view entry is present
      if (visitCount) {
        visitCount = Number(visitCount) + 1
        localStorage.setItem('page_view', visitCount)
      } else {
        visitCount = 1
        localStorage.setItem('page_view', 1)
      }
      counterContainer.innerHTML = visitCount


      // Create Light and Dark Arrays 
      const lightThemes = [{
        'id': 'default',
        'handle': 'defaultStyle',
        'name': 'Default',
        'lightCss': `
            body { 
              /* fill the full viewport */
              min-height: 100vh;
              margin: 0;
              /* repeating diagonal stripes */
              background-image: repeating-linear-gradient(
                  var(--stripe-angle),
                  var(--stripe-color) 0,
                  var(--stripe-color) var(--stripe-width),
                transparent var(--stripe-width),
                  transparent var(--stripe-spacing)
              );
              font-family: 'Comic Sans MS', 'Comic Sans', cursive;
            }
            /* Ensure main content scrolls above video */
            main {
              position: relative;
              max-width: 85%;
              align-items: center;
              justify-content: center;
              padding: 0;
              /* Remove any padding */
              margin: auto;
              /* Remove any margins */
              top: 5vh;
              max-width: 1200px;
              /* Adjust this value as needed */
              display: flex;
              flex-wrap: wrap !important;
              gap: 20px;
            }
            .content-wrapper {
                display: block;
                position: relative;
                left: 0;
                top: 0;
              }
              .header-content h1 { 
                color: #ff0000; 
                font-size: 3vw;
                margin-bottom: 5px;
              }
              .header-content p { 
                color: #ffa600; 
                font-size: 1.5vw;
              }
            .era-timeline { 
              display: block !important;
              background: var(--bg-color);
              opacity: var(--opacity);
            }
            .style-card {
              background: #ffffcc;
              border: 3px outset #cc9966;
            }
            .style-description {
              color: #333333;
            }
            .style-title {
              color: #990000;
              font-weight: bold;
            }
            .style-links a {
              color: #0000ff;
              text-decoration: underline;
            }
            .extra-under { display: block !important; }
            .mermaid {
              background:rgb(245, 245, 243);
              color:rgb(42, 44, 20);
            }
            /* CSS editor styles */
            #css-editor,
            #html-editor {
              font-family: 'Courier New', monospace;
              line-height: 1.5;
              padding: 10px;
              border-radius: 4px;
              margin: 10px 0;
              border: 1px solid #ccc;
              background-color: #f9f9f9;
              resize: vertical;
            }
            
            .css-editor {
              margin: auto;
              max-width: 800vw;
              min-width: 500px;
              min-height: 150px;
              text-align: center;
              display: block;
            }
            
            .css-editor {
              margin: auto;
              max-width: 800vw;
              min-width: 500px;
              min-height: 150px;
              text-align: center;
              display: block;
            }
            
            .html-editor {
              margin: auto;
              max-width: calc(100% - 20px);
              min-width: 500px;
              min-height: 500px;
              text-align: center;
              display: block;
            }
            .style-card {
              cursor: pointer;
              transition: transform 0.2s ease, box-shadow 0.2s ease;
            }
            .style-card:hover {
              transform: translateY(-3px);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
            /* Tooltip to indicate clickable elements */
            [data-tooltip]:before {
              content: attr(data-tooltip);
              position: absolute;
              opacity: 0;
              transition: all 0.15s ease;
              padding: 5px 10px;
              color: #fff;
              border-radius: 4px;
              background: rgba(0, 0, 0, 0.8);
              pointer-events: none;
              z-index: 10;
              white-space: nowrap;
              transform: translateY(10px);
            }
            [data-tooltip]:hover:before {
              opacity: 1;
              transform: translateY(0);
            }
          `,
        'darkCss': `
          body { 
            /* fill the full viewport */
            min-height: 100vh;
            margin: 0;
  
            /* repeating diagonal stripes */
            background-image: repeating-linear-gradient(
                calc(3.125 * var(--stripe-angle)),
                var(--dark-stripe-color) 0,
                var(--dark-stripe-color) var(--stripe-width),
                rgba(0, 0, 0, 0.8) calc(1.125 * var(--stripe-width)),
                rgba(0, 0, 0, 0.8) var(--stripe-spacing)
            );
            font-family: 'Comic Sans MS', 'Comic Sans', cursive;
          }
              main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .content-wrapper {
            display: block;
            position: relative;
            left: 0;
            top: 0;
          }
          .header-content h1 { 
            color: #ff0000; 
            font-size: 6vw;
            margin-bottom: 5px;
          }
          .header-content p { 
            color: #ffa600; 
            font-size: 4vw;
          }
          .era-timeline { 
            display: block !important;
            background: var(--inverse-bg-color, #222);
            opacity: var(--opacity, 0.8);
          }
          .style-links a {
            color: #66ccff;
            text-decoration: underline;
          }
          .extra-under { display: block !important; }
          .mermaid {
            background: #333300;
            color: #f0f0f0;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
          }, {
            'id': 'vernacular',
            'handle': 'vernacularStyle',
            'name': 'Vernacular / Geocities',
            'lightCss': `
              body { 
                /* fill the full viewport */
                min-height: 100vh;
                margin: 0;
                /* repeating diagonal stripes */
                background-image: repeating-linear-gradient(
                    var(--stripe-angle),
                    var(--stripe-color) 0,
                    var(--stripe-color) var(--stripe-width),
                  transparent var(--stripe-width),
                    transparent var(--stripe-spacing)
                );
                font-family: 'Comic Sans MS', 'Comic Sans', cursive;
              }
              .content-wrapper {
                  display: block;
                  position: relative;
                  left: 0;
                  top: 0;
                }
            .header-content h1 { 
              color: #ff0000; 
              font-size: 3vw;
              margin-bottom: 5px;
            }
            .header-content p { 
              color: #ffa600; 
              font-size: 1.5vw;
            }
          .era-timeline { 
            display: block !important;
            background: var(--bg-color);
            opacity: var(--opacity);
          }
          .style-card {
            background: #ffffcc;
          }
          .style-description {
            color: #333333;
          }
          .style-title {
            color: #990000;
            font-weight: bold;
          }
          .style-links a {
            color: #0000ff;
            text-decoration: underline;
          }
          .extra-under { display: block !important; }
          .mermaid {
            background:rgb(245, 245, 243);
            color:rgb(42, 44, 20);
          }
          .counter {
            display: flex;
            flex: row wrap;
            padding: 2rem;
          }
          #under-construction {
            position: absolute;
            margin: 2rem;
            width: 100%;
            height: 100%;
            left: 40%;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
          'darkCss': `
          body { 
            /* fill the full viewport */
            min-height: 100vh;
            margin: 0;
  
            /* repeating diagonal stripes */
            background-image: repeating-linear-gradient(
                calc(3.125 * var(--stripe-angle)),
                var(--dark-stripe-color) 0,
                var(--dark-stripe-color) var(--stripe-width),
                rgba(0, 0, 0, 0.8) calc(1.125 * var(--stripe-width)),
                rgba(0, 0, 0, 0.8) var(--stripe-spacing)
            );
            font-family: 'Comic Sans MS', 'Comic Sans', cursive;
          }
          .content-wrapper {
              display: block;
              position: relative;
              left: 0;
              top: 0;
            }
            .header-content h1 { 
              color: #ff0000; 
              font-size: 3vw;
              margin-bottom: 5px;
            }
            .header-content p { 
              color: #ffa600; 
              font-size: 1.5vw;
            }
          .era-timeline { 
            display: block !important;
            background: var(--bg-color);
            opacity: var(--opacity);
          }
          .style-card {
            background: #ffffcc;
          }
          .style-description {
            color: #333333;
          }
          .style-title {
            color: #990000;
            font-weight: bold;
          }
          .style-links a {
            color: #0000ff;
            text-decoration: underline;
          }
          .extra-under { display: block !important; }
          .mermaid {
            background:rgb(245, 245, 243);
            color:rgb(42, 44, 20);
          }
          .counter {
            display: flex;
            flex: row wrap;
            padding: 2rem;
            color: #dddddd;
          }
          #under-construction {
            position: absolute;
            margin: 2rem;
            width: 100%;
            height: 100%;
            left: 40%;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'table-frames',
          'handle': 'table-framesStyle',
          'name': 'Table-Based Design',
          'lightCss': `
          body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            color: #333;
          }
          main {
            display: table;
            width: 100%;
            border-collapse: collapse;
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
          }
          .style-card {
            display: table-cell;
            border: 1px solid #999;
            padding: 10px;
            background: white;
          }
          #main-table { display: block !important; }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: Arial, sans-serif;
          background: #f0f0f0;
          color: #333;
        }
        main {
          display: table;
          width: 100%;
          border-collapse: collapse;
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
        }
        .style-card {
          display: table-cell;
          border: 1px solid #999;
          padding: 10px;
          background: white;
        }
        #main-table { display: block !important; }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'framesets',
        'handle': 'framesetsStyle',
        'name': 'Framesets',
        'lightCss': `
          body {
            margin: 0;
            padding: 0;
            font-family: Verdana, sans-serif;
          }
          .frame-nav, .frame-content {
            display: block !important;
            position: fixed;
            border: 2px inset #ccc;
            background: #f0f0f0;
          }
          .frame-nav {
            left: 0;
            top: 0;
            width: 200px;
            height: 100%;
            overflow: auto;
            padding: 10px;
          }
          .frame-content {
            left: 200px;
            top: 0;
            width: calc(100% - 200px);
            height: 100%;
            overflow: auto;
            padding: 10px;
            background: white;
          }
          main {
            margin-left: 200px;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          margin: 0;
          padding: 0;
          font-family: Verdana, sans-serif;
        }
        .frame-nav, .frame-content {
          display: block !important;
          position: fixed;
          border: 2px inset #ccc;
          background: #f0f0f0;
        }
        .frame-nav {
          left: 0;
          top: 0;
          width: 200px;
          height: 100%;
          overflow: auto;
          padding: 10px;
        }
        .frame-content {
          left: 200px;
          top: 0;
          width: calc(100% - 200px);
          height: 100%;
          overflow: auto;
          padding: 10px;
          background: white;
        }
        main {
          margin-left: 200px;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'flash',
        'handle': 'flashStyle',
        'name': 'Flash-Based Design',
        'lightCss': `
          body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            text-align: center;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .extra-flash-intro { 
            display: block !important; 
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 1000;
          }
          .style-card {
            background: #333;
            border-radius: 0;
            border: 1px solid #666;
            color: #fff;
          }
          .style-title {
            color: #ff9900;
          }
          .style-links a {
            color: #66ccff;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          background: #000;
          color: #fff;
          font-family: Arial, sans-serif;
          text-align: center;
        }
        /* Ensure main content scrolls above video */
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .extra-flash-intro { 
          display: block !important; 
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: #000;
          z-index: 1000;
        }
        .style-card {
          background: #333;
          border-radius: 0;
          border: 1px solid #666;
          color: #fff;
        }
        .style-title {
          color: #ff9900;
        }
        .style-links a {
          color: #66ccff;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'web2',
        'handle': 'web2Style',
        'name': 'Web 2.0 Glossy',
        'lightCss': `
          body {
            font-family: 'Lucida Grande', Arial, sans-serif;
            background: #f6f6f6;
          }
            /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            border-radius: 8px;
            border: 1px solid #ddd;
            background: linear-gradient(to bottom, #ffffff 0%, #f6f6f6 100%);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          .style-title {
            color: #0066cc;
            text-shadow: 0 1px 0 white;
          }
          .beta-badge {
            display: inline-block !important;
            background: #ff6600;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            margin-left: 5px;
            text-transform: uppercase;
            font-weight: bold;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Lucida Grande', Arial, sans-serif;
          background: #f6f6f6;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          border-radius: 8px;
          border: 1px solid #ddd;
          background: linear-gradient(to bottom, #ffffff 0%, #f6f6f6 100%);
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .style-title {
          color: #0066cc;
          text-shadow: 0 1px 0 white;
        }
        .beta-badge {
          display: inline-block !important;
          background: #ff6600;
          color: white;
          font-size: 10px;
          padding: 2px 5px;
          border-radius: 10px;
          margin-left: 5px;
          text-transform: uppercase;
          font-weight: bold;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'skeuomorphic',
        'handle': 'skeuomorphicStyle',
        'name': 'Skeuomorphic Design',
        'lightCss': `
          body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            position: relative;
            margin: 40px auto;
            width: 400px;
            height: 350px;
            background: #fff;
            border-radius: 1px;
          }
          /* Post-it note colors for each card */
          .style-card:nth-child(5n+1) {
            background: #FFFA99; /* Classic yellow */
          }
          .style-card:nth-child(5n+2) {
            background: #FF9E9E; /* Salmon pink */
          }
          .style-card:nth-child(5n+3) {
            background: #9EEBFF; /* Sky blue */
          }
          .style-card:nth-child(5n+4) {
            background: #B1FF9E; /* Mint green */
          }
          .style-card:nth-child(5n+5) {
            background: #E19EFF; /* Lavender */
          }
          .style-card::before,
          .style-card::after {
            content: '';
            position: absolute;
            bottom: 10px;
            width: 40%;
            height: 10px;
            box-shadow: 0 5px 14px rgba(0,0,0,.7);
            z-index: -1;
            transition: all .3s ease-in-out;
          }

          .style-card::before {
            left: 15px;
            transform: skew(-5deg) rotate(-5deg);
          }

          .style-card::after {
            right: 15px;
            transform: skew(5deg) rotate(5deg);
          }

          .style-card:hover::before,
          .style-card:hover::after {
            box-shadow: 0 2px 14px rgba(0,0,0,.4);
          }

          .style-card:hover::before {
            left: 5px;
          }

          .style-card:hover::after {
            right: 5px;
          }
          .style-title {
            font-size: 1.6rem;
            cursor: pointer;
            color: rgba(0, 122, 204, 0.9);
            display: inline-block;
            margin-bottom: 0.25rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 400;
            font-style: normal;
            padding: 0 0 0 2rem;
          }
          .style-era {
            font-style: italic;
            margin-bottom: 0.5rem;
            padding: 0 0 0 2rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 300;
            font-style: normal;
            opacity: 0.7;
          }
          
          .style-description {
            margin-bottom: 0.5rem;
            padding: 0 1rem 0 2rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 300;
            font-style: normal;
            opacity: 0.7;
          }
          
          .style-links a {
            margin-right: 0.5rem;
            color: #007acc;
            padding: 0 0 0 2rem;
            font-family: "Permanent Marker", cursive;
            font-weight: 100;
            font-style: normal;
            opacity: 0.7;
          }
          .era-timeline {
            display: block !important;
            background: url('https://img.freepik.com/free-photo/white-gypsum-wall_1194-6990.jpg?t=st=1746443052~exp=1746446652~hmac=a2420a81ff31a63454846c9c05c450b65b5a3982ffc27fd89a63d2b85f7ca563&w=1380');
            pointer-events: none;
            
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
        `,
        'darkCss': `
          body {
            background: #222;
            font-family: 'Helvetica Neue', Arial, sans-serif;
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: url('https://i.imgur.com/Ncrkb8L.png') repeat;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.5);
            border: 1px solid #444;
            color: #e0e0e0;
          }
          .card-texture {
            display: block !important;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('https://i.imgur.com/FL3SmIu.jpeg');
            opacity: 0.05;
            pointer-events: none;
          }
          .style-title {
            color: #bb86fc;
          }
          #css-editor {
            background-color: #333;
            color: #e0e0e0;
            border-color: #444;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.5);
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'swiss',
          'handle': 'swissStyle',
          'name': 'Swiss Style Design',
          'lightCss': `
          body {
            font-family: Helvetica, Arial, sans-serif;
            background: white;
            color: black;
            line-height: 1.5;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          main {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-gap: 20px;
          }
          .style-card {
            grid-column: span 4;
            background: white;
            border: none;
            border-bottom: 2px solid black;
            border-radius: 0;
            padding: 20px 0;
          }
          .style-title {
            font-size: 18px;
            font-weight: bold;
            color: black;
          }
          .grid-overlay {
            display: block !important;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
              90deg,
              rgba(255,0,0,0.03),
              rgba(255,0,0,0.03) 8.33%,
              transparent 8.33%,
              transparent 16.66%
            );
            pointer-events: none;
            z-index: 1000;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
          'darkCss': `
          body {
            font-family: Helvetica, Arial, sans-serif;
            background: #121212;
            color: white;
            line-height: 1.5;
          }
          main {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-gap: 20px;
        
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */

            gap: 20px;
          }
          .style-card {
            grid-column: span 4;
            background: #1a1a1a;
            border: none;
            border-bottom: 2px solid white;
            border-radius: 0;
            padding: 20px 0;
            color: white;
          }
          .style-title {
            font-size: 18px;
            font-weight: bold;
            color: white;
          }
          .style-links a {
            color: #bb86fc;
          }
          #css-editor {
            background-color: #1a1a1a;
            color: white;
            border-color: white;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'minimalist',
          'handle': 'minimalistStyle',
          'name': 'Minimalist',
          'lightCss': `
          body {
            font-family: 'Inter', sans-serif;
            background: white;
            color: #333;
            line-height: 1.6;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: white;
            border: none;
            border-radius: 0;
            padding: 30px 0;
            box-shadow: none;
          }
          .style-title {
            font-weight: 300;
            font-size: 24px;
            color: #000;
          }
          .style-description {
            font-weight: 300;
          }
          .style-links a {
            color: #000;
            text-decoration: none;
            border-bottom: 1px solid #000;
            padding-bottom: 2px;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
            font-family: 'Inter', sans-serif;
            background: #121212;
            color: #e0e0e0;
            line-height: 1.6;
          }
            main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: #1a1a1a;
            border: none;
            border-radius: 0;
            padding: 30px 0;
            box-shadow: none;
            color: #e0e0e0;
          }
          .style-title {
            font-weight: 300;
            font-size: 24px;
            color: #fff;
          }
          .style-description {
            font-weight: 300;
          }
          .style-links a {
            color: #bb86fc;
            text-decoration: none;
            border-bottom: 1px solid #bb86fc;
            padding-bottom: 2px;
          }
          #css-editor {
            background-color: #1a1a1a;
            color: #e0e0e0;
            border: none;
            border-bottom: 1px solid #333;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'responsive',
          'handle': 'responsiveStyle',
          'name': 'Responsive Design',
          'lightCss': `
          body {
            font-family: 'Open Sans', sans-serif;
            background: #f9f9f9;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
          .style-card {
            flex: 0 0 calc(33.333% - 20px);
            margin-bottom: 30px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
          }
          @media (max-width: 992px) {
            .style-card {
              flex: 0 0 calc(50% - 15px);
            }
          }
          @media (max-width: 576px) {
            .style-card {
              flex: 0 0 100%;
            }
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `,
      'darkCss': `
        body {
          font-family: 'Open Sans', sans-serif;
          background: #f9f9f9;
        }
        main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
        .style-card {
          flex: 0 0 calc(33.333% - 20px);
          margin-bottom: 30px;
          background: white;
          border-radius: 5px;
          box-shadow: 0 2px 5px rgba(0,0,0,0.1);
          transition: all 0.3s ease;
        }
        @media (max-width: 992px) {
          .style-card {
            flex: 0 0 calc(50% - 15px);
          }
        }
        @media (max-width: 576px) {
          .style-card {
            flex: 0 0 100%;
          }
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'mobile-first',
        'handle': 'mobile-firstStyle',
        'name': 'Mobile-First Design',
        'lightCss': `
          body {
            font-family: 'Roboto', sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            background: #f5f5f5;
          }
          main {
            padding: 10px;
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            width: 100%;
            margin-bottom: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 15px;
          }
          @media (min-width: 768px) {
            main {
              padding: 20px;
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
            }
            .style-card {
              width: calc(50% - 10px);
            }
          }
          @media (min-width: 992px) {
            .style-card {
              width: calc(33.333% - 15px);
            }
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Roboto', sans-serif;
          font-size: 16px;
          line-height: 1.5;
          color: #333;
          background: #f5f5f5;
        }
        main {
          padding: 10px;
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          width: 100%;
          margin-bottom: 15px;
          background: white;
          border-radius: 5px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          padding: 15px;
        }
        @media (min-width: 768px) {
          main {
            padding: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
          }
          .style-card {
            width: calc(50% - 10px);
          }
        }
        @media (min-width: 992px) {
          .style-card {
            width: calc(33.333% - 15px);
          }
        }
        /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'flat',
        'handle': 'flatStyle',
        'name': 'Flat Design',
        'lightCss': `
          body {
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            background: #f9f9f9;
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: none;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
          }
          .style-title {
            color: #0078d7;
          }
          .style-links a {
            color: #0078d7;
            text-decoration: none;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
      body {
          font-family: 'Segoe UI', 'Roboto', sans-serif;
          background: #f9f9f9;
          color: #333;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: white;
          border: none;
          border-radius: 2px;
          box-shadow: 0 1px 2px rgba(0,0,0,0.1);
          transition: all 0.2s ease;
        }
        .style-title {
          color: #0078d7;
        }
        .style-links a {
          color: #0078d7;
          text-decoration: none;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'semi-flat',
        'handle': 'semi-flatStyle',
        'name': 'Semi-Flat Design',
        'lightCss': `
          body {
            font-family: 'SF Pro Text', 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
          }
          .style-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
          }
          .style-title {
            color: #007aff;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'SF Pro Text', 'Roboto', sans-serif;
          background: #f5f5f5;
          color: #333;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: white;
          border: none;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          transition: all 0.3s ease;
        }
        .style-card:hover {
          box-shadow: 0 5px 15px rgba(0,0,0,0.1);
          transform: translateY(-2px);
        }
        .style-title {
          color: #007aff;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'parallax',
        'handle': 'parallaxStyle',
        'name': 'Parallax Design',
        'lightCss': `
          body {
            font-family: 'Montserrat', sans-serif;
            background: #222;
            color: white;
            perspective: 1px;
            height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            position: relative;
          }
          
          /* Parallax background layers */
          .parallax-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
          }
          
          .parallax-layer {
            position: absolute;
            width: 200%;
            height: 100%;
            background-repeat: repeat-x;
            background-size: auto 100%;
            will-change: transform;
          }
          
          .layer-1 {
            background-image: url('https://images.unsplash.com/photo-1506318137071-a8e063b4bec0?w=1200');
            animation: parallaxScroll 60s linear infinite;
            opacity: 0.3;
          }
          
          .layer-2 {
            background-image: url('https://images.unsplash.com/photo-1534447677768-be436bb09401?w=1200');
            animation: parallaxScroll 40s linear infinite;
            opacity: 0.2;
          }
          
          .layer-3 {
            background-image: url('https://images.unsplash.com/photo-1520034475321-cbe63696469a?w=1200');
            animation: parallaxScroll 20s linear infinite;
            opacity: 0.1;
          }
          
          @keyframes parallaxScroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
          }
          
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            transform-style: preserve-3d;
            z-index: 1;
          }
          
          .style-card {
            background: rgba(255,255,255,0.8);
            color: #333;
            border: none;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transform: translateZ(0);
            backdrop-filter: blur(3px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }
          
          .style-card:hover {
            transform: translateZ(5px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
          }
          
          /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Montserrat', sans-serif;
          background: #111;
          color: white;
          perspective: 1px;
          height: 100vh;
          overflow-x: hidden;
          overflow-y: auto;
          position: relative;
        }
        
        /* Parallax background layers */
        .parallax-bg {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: -1;
          overflow: hidden;
        }
        
        .parallax-layer {
          position: absolute;
          width: 200%;
          height: 100%;
          background-repeat: repeat-x;
          background-size: auto 100%;
          will-change: transform;
        }
        
        .layer-1 {
          background-image: url('https://images.unsplash.com/photo-1506318137071-a8e063b4bec0?w=1200');
          animation: parallaxScroll 60s linear infinite;
          opacity: 0.3;
        }
        
        .layer-2 {
          background-image: url('https://images.unsplash.com/photo-1534447677768-be436bb09401?w=1200');
          animation: parallaxScroll 40s linear infinite;
          opacity: 0.2;
          filter: hue-rotate(180deg) brightness(0.5);
        }
        
        .layer-3 {
          background-image: url('https://images.unsplash.com/photo-1520034475321-cbe63696469a?w=1200');
          animation: parallaxScroll 20s linear infinite;
          opacity: 0.1;
          filter: hue-rotate(180deg) brightness(0.5);
        }
        
        @keyframes parallaxScroll {
          0% { transform: translateX(0); }
          100% { transform: translateX(-50%); }
        }
        
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          margin: auto;
          top: 5vh;
          max-width: 1200px;
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
          transform-style: preserve-3d;
          z-index: 1;
        }
        
        .style-card {
          background: rgba(30,30,30,0.8);
          color: #eee;
          border: none;
          border-radius: 5px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.5);
          transform: translateZ(0);
          backdrop-filter: blur(3px);
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .style-card:hover {
          transform: translateZ(5px) scale(1.02);
          box-shadow: 0 8px 25px rgba(0,0,0,0.6);
        }
        
        /* CSS editor styles */
        #css-editor,
        #html-editor {
          font-family: 'Courier New', monospace;
          line-height: 1.5;
          padding: 10px;
          border-radius: 4px;
          margin: 10px 0;
          border: 1px solid #444;
          background-color: #222;
          color: #eee;
          resize: vertical;
        }
        
        .css-editor {
          margin: auto;
          max-width: 800vw;
          min-width: 500px;
          min-height: 150px;
          text-align: center;
          display: block;
        }
        
        .html-editor {
          margin: auto;
          max-width: calc(100% - 20px);
          min-width: 500px;
          min-height: 500px;
          text-align: center;
          display: block;
        }
        /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'spa',
        'handle': 'single-page-appStyle',
        'name': 'Single-Page App Design',
        'lightCss': `
          body {
            font-family: 'Roboto', sans-serif;
            background: #fafafa;
            color: #333;
            padding-top: 60px;
          }
              /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 100;
            padding: 15px 0;
          }
          .style-card {
            background: white;
            border: none;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12);
            transition: all 0.3s cubic-bezier(.25,.8,.25,1);
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Roboto', sans-serif;
          background: #fafafa;
          color: #333;
          padding-top: 60px;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        header {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          background: white;
          box-shadow: 0 2px 5px rgba(0,0,0,0.1);
          z-index: 100;
          padding: 15px 0;
        }
        .style-card {
          background: white;
          border: none;
          border-radius: 2px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.12);
          transition: all 0.3s cubic-bezier(.25,.8,.25,1);
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'pwa',
        'handle': 'pwaStyle',
        'name': 'Progressive Web App Design',
        'lightCss': `
          body {
            font-family: 'Google Sans', 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #202124;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
          }
          main {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 16px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 24px;
            z-index: 1;
          }
          header {
            position: sticky;
            top: 0;
            background: #1a73e8;
            color: white;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .style-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            will-change: transform;
          }
          .style-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.16), 0 4px 8px rgba(0,0,0,0.23);
          }
          .style-title {
            color: #1a73e8;
            font-size: 1.25rem;
            font-weight: 500;
          }
          #css-editor,
          #html-editor {
            font-family: 'Roboto Mono', monospace;
            line-height: 1.5;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            border: 1px solid #dadce0;
            background-color: #f8f9fa;
            resize: vertical;
            transition: box-shadow 0.2s;
          }
          #css-editor:focus,
          #html-editor:focus {
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.4);
            outline: none;
          }
          .css-editor, .html-editor {
            margin: auto;
            width: 100%;
            max-width: 800px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Google Sans', 'Roboto', sans-serif;
            background: #202124;
            color: #e8eaed;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
          }
          main {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 16px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 24px;
            z-index: 1;
          }
          header {
            position: sticky;
            top: 0;
            background: #1a73e8;
            color: white;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
          .style-card {
            background: #2d2e30;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.25), 0 1px 2px rgba(0,0,0,0.35);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            will-change: transform;
          }
          .style-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3), 0 4px 8px rgba(0,0,0,0.4);
          }
          .style-title {
            color: #8ab4f8;
            font-size: 1.25rem;
            font-weight: 500;
          }
          #css-editor,
          #html-editor {
            font-family: 'Roboto Mono', monospace;
            line-height: 1.5;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
            border: 1px solid #5f6368;
            background-color: #2d2e30;
            color: #e8eaed;
            resize: vertical;
            transition: box-shadow 0.2s;
          }
          #css-editor:focus,
          #html-editor:focus {
            box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.4);
            outline: none;
          }
          .css-editor, .html-editor {
            margin: auto;
            width: 100%;
            max-width: 800px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'cinematic',
        'handle': 'cinematicStyle',
        'name': 'Cinematic Design',
        'lightCss': `
          body {
            font-family: 'Playfair Display', serif;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
            margin: 0;
            padding: 0;
            background: #000;
            letter-spacing: 0.03em;
          }
          .hero-section, .content-wrapper {
            display: block !important;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 1;
          }
          .era-timeline {
            top: 0;
            left: 0;
            width: 80%;
            margin: auto;
            padding: 2rem;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
          }
          .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            filter: contrast(1.1) saturate(1.2);
          }
          .style-card {
            background: rgb(15, 15, 20, 0.8);
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            transform: perspective(1000px) rotateX(0deg);
          }
          .style-card:hover {
            transform: perspective(1000px) rotateX(2deg) translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.9);
            border-color: rgba(255, 255, 255, 0.4);
          }
          .style-description {
            color: #ccc;
            font-weight: 300;
            line-height: 1.6;
            letter-spacing: 0.02em;
          }
          .style-title {
            color: #e6c07b;
            font-weight: bold;
            font-size: 1.8rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(230, 192, 123, 0.3);
            padding-bottom: 0.5rem;
          }
          .style-links a {
            color: #61afef;
            text-decoration: none;
            border: 1px solid rgba(97, 175, 239, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 2px;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
            display: inline-block;
          }
          .style-links a:hover {
            background: rgba(97, 175, 239, 0.1);
            border-color: rgba(97, 175, 239, 0.6);
            transform: translateY(-2px);
          }
          .extra-under { display: block !important; }
          .mermaid {
            background: rgba(20, 20, 25, 0.9);
            color: #ddd;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
          }
          #css-editor,
          #html-editor {
            font-family: 'Fira Code', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(20, 20, 25, 0.9);
            color: #ddd;
            resize: vertical;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
          }
          .css-editor, .html-editor {
            margin: auto;
            max-width: 800px;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Playfair Display', serif;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
            margin: 0;
            padding: 0;
            background: #000;
            letter-spacing: 0.03em;
          }
          .hero-section, .content-wrapper {
            display: block !important;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
            z-index: 1;
          }
          .era-timeline {
            top: 0;
            left: 0;
            width: 80%;
            margin: auto;
            padding: 2rem;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
          }
          .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            filter: contrast(1.1) saturate(1.2);
          }
          .style-card {
            background: rgba(15, 15, 20, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            transform: perspective(1000px) rotateX(0deg);
          }
          .style-card:hover {
            transform: perspective(1000px) rotateX(2deg) translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.9);
            border-color: rgba(255, 255, 255, 0.4);
          }
          .style-description {
            color: #ccc;
            font-weight: 300;
            line-height: 1.6;
            letter-spacing: 0.02em;
          }
          .style-title {
            color: #e6c07b;
            font-weight: bold;
            font-size: 1.8rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
            margin-bottom: 1rem;
            border-bottom: 1px solid rgba(230, 192, 123, 0.3);
            padding-bottom: 0.5rem;
          }
          .style-links a {
            color: #61afef;
            text-decoration: none;
            border: 1px solid rgba(97, 175, 239, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 2px;
            transition: all 0.3s ease;
            margin-right: 0.5rem;
            display: inline-block;
          }
          .style-links a:hover {
            background: rgba(97, 175, 239, 0.1);
            border-color: rgba(97, 175, 239, 0.6);
            transform: translateY(-2px);
          }
          .extra-under { display: block !important; }
          .mermaid {
            background: rgba(20, 20, 25, 0.9);
            color: #ddd;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
          }
          #css-editor,
          #html-editor {
            font-family: 'Fira Code', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(20, 20, 25, 0.9);
            color: #ddd;
            resize: vertical;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
          }
          .css-editor, .html-editor {
            margin: auto;
            max-width: 800px;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'brutalist',
          'handle': 'brutalistStyle',
          'name': 'BRUTALIST',
          'lightCss': `
          body {
            font-family: monospace;
            background: white;
            color: black;
            margin: 0;
            padding: 0;
            line-height: 1;
          }
          main {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 2fr));
            gap: 30px;
            justify-content: center;
          }
          .style-card {
            background: white;
            border: 4px solid black;
            border-radius: 0;
            padding: 1rem;
            margin: 0;
            box-shadow: none;
          }
          .style-title {
            font-size: 24px;
            text-transform: uppercase;
            letter-spacing: -1px;
            margin-bottom: 20px;
            border-bottom: 4px solid black;
            padding-bottom: 10px;
          }
          .style-description {
            margin: 20px 0;
            text-transform: uppercase;
            font-size: 14px;
          }
          .style-links a {
            display: inline-block;
            background: black;
            color: white;
            padding: 10px 15px;
            margin: 5px 0;
            text-decoration: none;
            text-transform: uppercase;
            border: none;
          }
          .style-links a:hover {
            background: white;
            color: black;
            outline: 4px solid black;
          }
          #css-editor, #html-editor {
            background-color: black;
            color: white;
            border: 4px solid white;
            font-family: monospace;
            border-radius: 0;
            padding: 20px;
            resize: none;
          }
          .css-editor, .html-editor {
            margin: 30px 0;
            width: 100%;
            min-height: 200px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: monospace;
            background: black;
            color: white;
            margin: 0;
            padding: 0;
            line-height: 1;
          }
          main {
            max-width: 100%;
            margin: 0;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
          }
          .style-card {
            background: black;
            border: 4px solid white;
            border-radius: 0;
            padding: 1rem;
            margin: 0;
            box-shadow: none;
            color: white;
          }
          .style-title {
            font-size: 24px;
            text-transform: uppercase;
            letter-spacing: -1px;
            margin-bottom: 20px;
            border-bottom: 4px solid white;
            padding-bottom: 10px;
            color: white;
          }
          .style-description {
            margin: 20px 0;
            text-transform: uppercase;
            font-size: 14px;
          }
          .style-links a {
            display: inline-block;
            background: white;
            color: black;
            padding: 10px 15px;
            margin: 5px 0;
            text-decoration: none;
            text-transform: uppercase;
            border: none;
          }
          .style-links a:hover {
            background: black;
            color: white;
            outline: 4px solid white;
          }
          #css-editor, #html-editor {
            background-color: black;
            color: white;
            border: 4px solid white;
            font-family: monospace;
            border-radius: 0;
            padding: 20px;
            resize: none;
          }
          .css-editor, .html-editor {
            margin: 30px 0;
            width: 100%;
            min-height: 200px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'neo-brutalism',
          'handle': 'neo-brutalismStyle',
          'name': 'Neo-Brutalism Design',
          'lightCss': `
          body {
            font-family: 'Arial Black', 'Impact', sans-serif;
            background: #ffde59;
            color: black;
            overflow-x: hidden;
          }
          main {
            position: relative;
            max-width: 90%;
            margin: 2rem auto;
            display: flex;
            flex-wrap: wrap !important;
            gap: 30px;
          }
          .style-card {
            background: white;
            border: 4px solid black;
            border-radius: 0;
            box-shadow: 8px 8px 0 black;
            transform: rotate(-1.5deg);
            transition: all 0.2s ease;
            padding: 25px;
          }
          .style-card:hover {
            transform: rotate(1deg) translateY(-5px);
            box-shadow: 12px 12px 0 black;
          }
          .style-title {
            font-size: 1.8rem;
            text-transform: uppercase;
            color: #ff3366;
            -webkit-text-stroke: 1px black;
          }
          .style-links a {
            display: inline-block;
            background: #00ddff;
            color: black;
            font-weight: bold;
            text-decoration: none;
            padding: 8px 15px;
            margin: 5px;
            border: 3px solid black;
            box-shadow: 4px 4px 0 black;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            background: #ff3366;
            transform: translateY(-3px);
            box-shadow: 6px 6px 0 black;
          }
          #css-editor, #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border: 4px solid black;
            border-radius: 0;
            background-color: white;
            box-shadow: 8px 8px 0 black;
            resize: vertical;
            transform: rotate(-1deg);
          }
          .css-editor, .html-editor {
            margin: 2rem auto;
            min-width: 500px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Arial Black', 'Impact', sans-serif;
            background: #121212;
            color: white;
            overflow-x: hidden;
          }
          main {
            position: relative;
            max-width: 90%;
            margin: 2rem auto;
            display: flex;
            flex-wrap: wrap !important;
            gap: 30px;
          }
          .style-card {
            background: #1a1a1a;
            border: 4px solid #00ddff;
            border-radius: 0;
            box-shadow: 8px 8px 0 #00ddff;
            transform: rotate(-1.5deg);
            transition: all 0.2s ease;
            padding: 25px;
          }
          .style-card:hover {
            transform: rotate(1deg) translateY(-5px);
            box-shadow: 12px 12px 0 #00ddff;
          }
          .style-title {
            font-size: 1.8rem;
            text-transform: uppercase;
            color: #ff3366;
            -webkit-text-stroke: 1px #00ddff;
          }
          .style-links a {
            display: inline-block;
            background: #00ddff;
            color: black;
            font-weight: bold;
            text-decoration: none;
            padding: 8px 15px;
            margin: 5px;
            border: 3px solid white;
            box-shadow: 4px 4px 0 white;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            background: #ff3366;
            transform: translateY(-3px);
            box-shadow: 6px 6px 0 white;
          }
          #css-editor, #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border: 4px solid #00ddff;
            border-radius: 0;
            background-color: #1a1a1a;
            color: white;
            box-shadow: 8px 8px 0 #00ddff;
            resize: vertical;
            transform: rotate(-1deg);
          }
          .css-editor, .html-editor {
            margin: 2rem auto;
            min-width: 500px;
            min-height: 150px;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'dark-mode',
          'handle': 'dark-modeStyle',
          'name': 'Dark Mode Design',
          'lightCss': `
          body {
            font-family: 'SF Pro Text', 'Roboto', sans-serif;
            background: #121212;
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 8px;
            color: #e0e0e0;
          }
          .style-title {
            color: #bb86fc;
          }
          .style-links a {
            color: #03dac6;
          }
          #css-editor {
            background-color: #2a2a2a;
            color: #e0e0e0;
            border-color: #444;
          }
          /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #2a2a2a;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'SF Pro Text', 'Roboto', sans-serif;
          background: #0a0a0a;
          color: #e0e0e0;
        }
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          margin: auto;
          top: 5vh;
          max-width: 1200px;
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: #151515;
          border: 1px solid #333;
          border-radius: 8px;
          color: #e0e0e0;
          box-shadow: 0 0 15px rgba(187, 134, 252, 0.3);
          transition: all 0.3s ease;
        }
        .style-card:hover {
          box-shadow: 0 0 25px rgba(187, 134, 252, 0.5);
        }
        .style-title {
          color: #bb86fc;
          text-shadow: 0 0 8px rgba(187, 134, 252, 0.7);
        }
        .style-links a {
          color: #03dac6;
          text-shadow: 0 0 8px rgba(3, 218, 198, 0.7);
          transition: all 0.3s ease;
        }
        .style-links a:hover {
          color: #5df2e6;
          text-shadow: 0 0 12px rgba(3, 218, 198, 0.9);
        }
        #css-editor {
          background-color: #1a1a1a;
          color: #e0e0e0;
          border: 1px solid #444;
          box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8), 0 0 10px rgba(187, 134, 252, 0.2);
        }
        /* CSS editor styles */
        #css-editor,
        #html-editor {
          font-family: 'Courier New', monospace;
          line-height: 1.5;
          padding: 10px;
          border-radius: 4px;
          margin: 10px 0;
          resize: vertical;
        }
        
        .css-editor {
          margin: auto;
          max-width: 800vw;
          min-width: 500px;
          min-height: 150px;
          text-align: center;
          display: block;
        }
        
        .html-editor {
          margin: auto;
          max-width: calc(100% - 20px);
          min-width: 500px;
          min-height: 500px;
          text-align: center;
          display: block;
        }
        /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'neumorphism',
        'handle': 'neumorphismStyle',
        'name': 'Neumorphism Design',
        'lightCss': `
         body {
            font-family: 'Poppins', sans-serif;
            background: #e0e5ec;
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: #e0e5ec;
            border-radius: 20px;
            box-shadow: 10px 10px 20px #b8bec7, 
                        -10px -10px 20px #ffffff;
            border: none;
            padding: 25px;
            transition: all 0.3s ease;
          }
          .style-card:hover {
            box-shadow: 5px 5px 10px #b8bec7, 
                        -5px -5px 10px #ffffff;
          }
          .style-title {
            color: #555;
            font-weight: 600;
          }
          .style-links a {
            color: #555;
            text-decoration: none;
            padding: 8px 15px;
            margin-right: 10px;
            border-radius: 10px;
            background: #e0e5ec;
            box-shadow: 5px 5px 10px #b8bec7, 
                        -5px -5px 10px #ffffff;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            box-shadow: inset 3px 3px 6px #b8bec7, 
                        inset -3px -3px 6px #ffffff;
          }
          #css-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 20px;
            margin: 15px 0;
            border: none;
            background: #e0e5ec;
            box-shadow: inset 5px 5px 10px #b8bec7, 
                        inset -5px -5px 10px #ffffff;
            resize: vertical;
            color: #555;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
          body {
            font-family: 'Poppins', sans-serif;
            background: #2d3436;
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: #2d3436;
            border-radius: 20px;
            box-shadow: 10px 10px 20px #1a1f20, 
                        -10px -10px 20px #40494c;
            border: none;
            padding: 25px;
            color: #e0e0e0;
            transition: all 0.3s ease;
          }
          .style-card:hover {
            box-shadow: 5px 5px 10px #1a1f20, 
                        -5px -5px 10px #40494c;
          }
          .style-title {
            color: #bb86fc;
            font-weight: 600;
          }
          .style-links a {
            color: #03dac6;
            text-decoration: none;
            padding: 8px 15px;
            margin-right: 10px;
            border-radius: 10px;
            background: #2d3436;
            box-shadow: 5px 5px 10px #1a1f20, 
                        -5px -5px 10px #40494c;
            transition: all 0.2s ease;
          }
          .style-links a:hover {
            box-shadow: inset 3px 3px 6px #1a1f20, 
                        inset -3px -3px 6px #40494c;
          }
          #css-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 15px;
            border-radius: 20px;
            margin: 15px 0;
            border: none;
            background: #2d3436;
            box-shadow: inset 5px 5px 10px #1a1f20, 
                        inset -5px -5px 10px #40494c;
            resize: vertical;
            color: #e0e0e0;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
      },
        {
          'id': 'glassmorphism',
          'handle': 'glassmorphismStyle',
          'name': 'Glassmorphism Design',
          'lightCss': `
          body {
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f0f4f8, #d7e3fc);
            color: #333;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          }
          .style-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
          }
          .style-title {
            color: #4361ee;
          }
          .style-links a {
            color: #4361ee;
            text-decoration: none;
          }
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 16px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
            font-family: 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0f172a, #1e293b);
            color: #e0e0e0;
          }
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: auto;
            top: 5vh;
            max-width: 1200px;
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            color: #e0e0e0;
            transition: all 0.3s ease;
          }
          .style-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
          }
          .style-title {
            color: #a78bfa;
          }
          .style-links a {
            color: #7dd3fc;
            text-decoration: none;
          }
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 16px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            color: #e0e0e0;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'corporate-memphis',
          'handle': 'corporate-memphisStyle',
          'name': 'Corporate Memphis',
          'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            background: #ffde59;
            color: black;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `body {
            font-family: 'Arial', sans-serif;
            background: #121212;
            color: #e0e0e0;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: #1a1a1a;
            border: 3px solid white;
            border-radius: 0;
            box-shadow: 5px 5px 0 white;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'retro-web',
          'handle': 'retro-webStyle',
          'name': 'Retro Web',
          'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            background: #ffde59;
            color: black;
          }
            /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `body {
            font-family: 'Arial', sans-serif;
            background: #121212;
            color: #e0e0e0;
          }
            /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: #1a1a1a;
            border: 3px solid white;
            border-radius: 0;
            box-shadow: 5px 5px 0 white;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'y2k-web',
          'handle': 'y2k-webStyle',
          'name': 'Y2K Web',
          'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            background: #ffde59;
            color: black;
          }
            /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }

          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `body {
            background: #1a1a1a;
            border: 3px solid #bb86fc;
            border-radius: 0;
            box-shadow: 5px 5px 0 #bb86fc;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
            color: #e0e0e0;
          }
            /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-title {
            color: #bb86fc;
          }
          .style-links a {
            color: #03dac6;
          }
          #css-editor {
            background-color: #1a1a1a;
            color: #e0e0e0;
            border: 3px solid #bb86fc;
            box-shadow: 5px 5px 0 #bb86fc;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
      {
        'id': 'y2k-web',
        'handle': 'y2k-webStyle',
        'name': 'Y2K Web Design',
        'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            background: #ffde59;
            color: black;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          } 
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
          'darkCss': `
          body {
          font-family: 'Arial', sans-serif;
          background: #ffde59;
          color: black;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;

          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `
        },
        {
          'id': 'webgl-3d',
          'handle': 'webgl-3dStyle',
          'name': 'WebGL & 3D Design',
          'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            color:rgba(230, 230, 230, 0.9);
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background:rgba(1, 36, 0, 0.9);
            border: 3px solid rgba(0, 255, 21, 0.9);
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `body {
          font-family: 'Arial', sans-serif;
          color:rgba(230, 230, 230, 0.9);
        }
        /* Ensure main content scrolls above video */
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background:rgba(1, 36, 0, 0.9);
          border: 3px solid rgba(0, 255, 21, 0.9);
          border-radius: 0;
          box-shadow: 5px 5px 0 black;
          transform: rotate(-1deg);
          transition: all 0.2s ease;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
      },
      {
        'id': 'inclusive-design',
        'handle': 'inclusive-designStyle',
        'name': 'Inclusive Design',
        'lightCss': `
          body {
            font-family: 'Arial', sans-serif;
            background: #ffde59;
            color: black;
          }
          /* Ensure main content scrolls above video */
          main {
            position: relative;
            max-width: 85%;
            align-items: center;
            justify-content: center;
            padding: 0;
            /* Remove any padding */
            margin: auto;
            /* Remove any margins */
            top: 5vh;
            max-width: 1200px;
            /* Adjust this value as needed */
            display: flex;
            flex-wrap: wrap !important;
            gap: 20px;
          }
          .style-card {
            background: white;
            border: 3px solid black;
            border-radius: 0;
            box-shadow: 5px 5px 0 black;
            transform: rotate(-1deg);
            transition: all 0.2s ease;
          }
                    /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
        `,
        'darkCss': `
        body {
          font-family: 'Arial', sans-serif;
          background: #ffde59;
          color: black;
        }
        /* Ensure main content scrolls above video */
        main {
          position: relative;
          max-width: 85%;
          align-items: center;
          justify-content: center;
          padding: 0;
          /* Remove any padding */
          margin: auto;
          /* Remove any margins */
          top: 5vh;
          max-width: 1200px;
          /* Adjust this value as needed */
          display: flex;
          flex-wrap: wrap !important;
          gap: 20px;
        }
        .style-card {
          background: white;
          border: 3px solid black;
          border-radius: 0;
          box-shadow: 5px 5px 0 black;
          transform: rotate(-1deg);
          transition: all 0.2s ease;
        }
                  /* CSS editor styles */
          #css-editor,
          #html-editor {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            resize: vertical;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .css-editor {
            margin: auto;
            max-width: 800vw;
            min-width: 500px;
            min-height: 150px;
            text-align: center;
            display: block;
          }
          
          .html-editor {
            margin: auto;
            max-width: calc(100% - 20px);
            min-width: 500px;
            min-height: 500px;
            text-align: center;
            display: block;
          }
          /* Style cards interactive styles */
          .style-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }
          
          .style-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
                
          /* Tooltip to indicate clickable elements */
          [data-tooltip]:before {
            content: attr(data-tooltip);
            position: absolute;
            opacity: 0;
            transition: all 0.15s ease;
            padding: 5px 10px;
            color: #fff;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.8);
            pointer-events: none;
            z-index: 10;
            white-space: nowrap;
            transform: translateY(10px);
          }
          
          [data-tooltip]:hover:before {
            opacity: 1;
            transform: translateY(0);
          }
      `
  }];

  // Create Dark Themes Array from existing themes
  const darkThemes = lightThemes.map(theme => ({
    id: theme.id,
    handle: theme.handle,
    name: theme.name,
    lightCss: theme.darkCss, // Use darkCss as lightCss for dark themes
    darkCss: theme.darkCss   // Keep darkCss the same
  }));

  // Define separate video sources for light and dark modes
  const videoSources = [
    "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4",
    "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4"
  ];

  const videoSources2 = [
    "https://mytech.today/wp-content/uploads/2025/06/dark-cinematic-bg-01.mp4",
    "https://mytech.today/wp-content/uploads/2025/06/dark-cinematic-bg-02.mp4"
  ];

  // Define isDarkMode in the global scope
  let isDarkMode = localStorage.getItem('darkMode') === 'true';

  // Function to get the appropriate video sources based on dark mode
  function getVideoSources() {
    return isDarkMode ? videoSources2 : videoSources;
  }

  // Refactored Theme Switcher with Separate Light and Dark Arrays
  jQuery(function($) {
    try {
      const videoSources = [
        "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4",
        "https://mytech.today/wp-content/uploads/2025/06/2010-Cinematic-Bg-05.mp4"
      ];

      const videoSources2 = [
        "https://mytech.today/wp-content/uploads/2025/04/tTrucks.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/Notgridlock.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/Gridlock.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/RTA_bus-2-12043240-3840-2160-24Fps.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/lsd-01-12057787-3840-2160-24Fps.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/lsd-02-.mp4",
        "https://mytech.today/wp-content/uploads/2025/04/lower-waker-bridge-2-2005974-Hd-1920-1080-24Fps.mp4"
      ];

      const $themeStyle = $('#theme-style');
      const $cssEditor = $('#css-editor');
      const $heroSection = $('.hero-section');
      const $contentWrapper = $('.content-wrapper');
      const $video1 = $('#color_videos_1');
      const $video2 = $('#color_videos_2');
      const $underConstruction = $('#under-construction');
      const $visitorCounter = $('.counter');

      let current = 0;
      const themes = getCurrentThemeSet();
      
      // Single video handling function
      function handleCinematicVideos(action) {
        if (action === 'start') {
          $heroSection.show();
          $contentWrapper.show();
          
          // Get the appropriate video sources based on dark mode
          const currentSources = getVideoSources();
          
          $video1.attr('src', currentSources[0]);
          $video2.attr('src', currentSources[1]);
          
          // Rest of your video handling code
          if (!$video1.attr('src')) {
            $video1.attr('src', videoSources[0]);
            $video2.attr('src', videoSources[1]);
            
            // Wait for videos to load before starting
            let loadedVideos = 0;
            const totalVideos = 2;
            
            const checkAndStart = () => {
              if (loadedVideos === totalVideos) {
                startVideoPlayback();
              }
            };
            
            $video1.on('loadeddata', () => {
              console.log('Video 1 loaded');
              loadedVideos++;
              checkAndStart();
            }).on('error', e => {
              console.error('Error loading video 1:', e);
            });
            
            $video2.on('loadeddata', () => {
              console.log('Video 2 loaded');
              loadedVideos++;
              checkAndStart();
            }).on('error', e => {
              console.error('Error loading video 2:', e);
            });
          } else {
            // Videos already loaded, just start playback
            startVideoPlayback();
          }
        } else if (action === 'stop') {
          $heroSection.hide();
          if ($video1[0]) $video1[0].pause();
          if ($video2[0]) $video2[0].pause();
        }
      }
      
      function startVideoPlayback() {
        $video1.css('opacity', '1');
        $video2.css('opacity', '0');
        $video1.css('display', 'block');
        
        // Clear previous event handlers to prevent duplicates
        $video1.off('ended');
        $video2.off('ended');
        
        $video1[0].play().catch(e => console.error('Play error video1:', e));
        
        $video1.on('ended', () => {
          $video1.css('opacity', '0');
          $video2.css('opacity', '1');
          $video2.css('display', 'block');
          $video2[0].play().catch(e => console.error('Play error video2:', e));
        });
        
        $video2.on('ended', () => {
          $video2.css('opacity', '0');
          $video1.css('opacity', '1');
          $video1[0].currentTime = 0;
          $video1[0].play().catch(e => console.error('Replay video1 failed:', e));
        });
      }

      // === Theme State Initialization ===
      let isDarkMode = localStorage.getItem('darkMode') === 'true';


      function formatCss(css) {
        try {
          if (!css) return '/* No CSS defined */';
          return css
            .replace(/}/g, '};\n')
            .replace(/{/g, ' {\n')
            .replace(/;(?!\n)/g, ';\n')
            .replace(/:(?! )/g, ': ')
            .trim();
        } catch (e) {
          console.error('CSS formatting error:', e);
          return '/* CSS formatting error */';
        }
      }

      function dropTheme() {
        $('body').removeClass(function(i, c) {
          return (c.match(/(^|\s)theme-\S+/g) || []).join(' ');
        });
      }

      function getCurrentThemeSet() {
        const isDark = $('#dark-mode-toggle').is(':checked');
        return isDark ? darkThemes : lightThemes;
      }

      // === Apply Theme by Index ===
      function applyTheme(index) {
        const themeSet = getCurrentThemeSet();
        const theme = themeSet[index];
        if (!theme) {
          console.error(`Invalid theme index: ${index}`);
          return;
        }

        const cssVars = isDarkMode ? theme.darkCss : theme.lightCss;
        if (!cssVars) {
          console.error(`Missing ${isDarkMode ? 'dark' : 'light'} CSS for theme "${theme.name}"`);
          return;
        }

        const prettyCss = prettier.format(cssVars, {
          parser: 'css',
          plugins: prettierPlugins,
          printWidth: 80,
          tabWidth: 2,
          useTabs: false
        });

        $themeStyle.html(prettyCss);
        $cssEditor.val(formatCss(prettyCss));

        // Update the current theme display
        $('#current-theme').text(theme.name);

        Object.entries(prettyCss).forEach(([key, value]) => {
          document.documentElement.style.setProperty(key, value);
        });

        // Dispatch theme changed event
        document.dispatchEvent(new CustomEvent('themeChanged', {
          detail: { themeName: theme.name, isDarkMode: isDarkMode }
        }));

        console.log(`Applied theme "${theme.name}" (index: ${index}) in ${isDarkMode ? 'Dark' : 'Light'} Mode`);
        localStorage.setItem('lastThemeIndex', index);

        // Handle cinematic mode
        if (theme.name.includes('Cinematic')) {
          handleCinematicVideos('start');
        } else {
          handleCinematicVideos('stop');
        }

        if (theme.name.includes('Vernacular')) {
          $underConstruction.show();
          $visitorCounter.show();
        } else {
          $underConstruction.hide();
          $visitorCounter.hide();
        }
      }

      // === Dark Mode Toggle Handler ===
      $('#dark-mode-toggle').on('change', function() {
        isDarkMode = this.checked;
        localStorage.setItem('darkMode', isDarkMode);

        // Reapply the current theme with the new dark/light mode setting
        const lastIndex = parseInt(localStorage.getItem('lastThemeIndex'), 10) || 0;
        applyTheme(lastIndex);
        
        console.log(`Dark Mode toggled: ${isDarkMode}`);
      });

      // === Load Theme from Local Storage on Page Load ===
      $(document).ready(function() {
        const lastIndex = parseInt(localStorage.getItem('lastThemeIndex'), 10);
        if (!isNaN(lastIndex)) {
          applyTheme(lastIndex);
        }

        $('#dark-mode-toggle').prop('checked', isDarkMode);
        
        // Bind all click handlers
        rebindClickHandlers();
      });

      // Function to rebind all click handlers
      function rebindClickHandlers() {
        // Remove existing handlers first
        $('.style-card').off('click');
        $('.style-title').off('click');
        $('.mermaid .task, .mermaid text.taskText').off('click');
        
        // Rebind style card clicks
        $('.style-card').on('click', function () {
          const index = $(this).data('index');
          if (typeof index === 'number') {
            console.log(`Style card clicked. Applying index: ${index}`);
            applyTheme(index);
            current = index;
            gtag('event', 'apply_theme_click', {
              event_category: 'Theme Card Click',
              event_label: getCurrentThemeSet()[index].handle,
              value: index
            });
          }
        });
        
        // Rebind style title clicks
        $('.style-title').on('click', function (e) {
          e.stopPropagation();
          const $card = $(this).closest('.style-card');
          const index = $card.data('index');
          if (typeof index === 'number') {
            console.log(`Style title clicked. Applying index: ${index}`);
            applyTheme(index);
            current = index;
            gtag('event', 'apply_theme_click', {
              event_category: 'Theme Title Click',
              event_label: getCurrentThemeSet()[index].handle,
              value: index
            });
          }
        });

        // Rebind Mermaid timeline task clicks
        $('.mermaid .task, .mermaid text.taskText').on('click', function () {
          const elementId = this.id;
          if (elementId) {
            // Find the theme with matching ID and apply it
            const themeSet = getCurrentThemeSet();
            const themeIndex = themeSet.findIndex(theme => theme.id === elementId);
            if (themeIndex !== -1) {
              applyTheme(themeIndex);
              current = themeIndex;
              console.log(`Applied theme by ID: ${elementId}, index: ${themeIndex}`);
            } else {
              console.warn(`No theme found with ID: ${elementId}`);
            }
          } else {
            console.warn('Clicked Mermaid element has no ID');
          }
        });
      }

      // CSS editor live updates
      $cssEditor.on('input', function () {
        const customCss = $(this).val();
        $themeStyle.html(customCss);
        console.log('Custom CSS applied from editor');
      });

      // Keyboard shortcut: Ctrl + Shift + Space to cycle styles
      $(document).on('keydown.themeCycle', function (e) {
        if (e.ctrlKey && e.shiftKey && e.keyCode === 32) {
          const themes = getCurrentThemeSet();
          const nextIndex = (current + 1) % themes.length;
          applyTheme(nextIndex);
          e.preventDefault();
        }
      });
    } catch (e) {
      console.error('Error with script', e);
    }
  });
  </script>
  </body>
</html>
